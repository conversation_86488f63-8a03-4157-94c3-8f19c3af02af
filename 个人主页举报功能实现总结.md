# 个人主页举报功能实现总结

## 需求概述
为个人主页更多按钮的举报功能创建自定义举报页面，支持个人信息举报和作品举报两种类型，使用阴影样式的容器，并根据API数据动态显示举报选项。

## 实现内容

### 1. 数据模型创建
**文件**: 
- `Shuxiaoqi/Modules/Model/Report/ReportUserInfoContentModel.swift`
- `Shuxiaoqi/Modules/Model/Report/ReportUserInfoReasonModel.swift`

**功能特点**:
- 为个人信息举报内容和理由API创建对应的数据模型
- 支持SmartCodable解析
- 包含UI状态管理属性（isSelected）
- 提供便利方法和枚举类型

### 2. API接口集成
**修改文件**: 
- `Shuxiaoqi/Common/NetworkManager/APIRouter.swift`
- `Shuxiaoqi/Common/NetworkManager/APIManager.swift`

**新增接口**:
- `/api/report/getReportUserInfoContentDict` - 获取个人信息举报内容字典
- `/api/report/getReportUserInfoReasontDict` - 获取个人信息举报理由字典

### 3. 举报控制器实现
**文件**: `Shuxiaoqi/Modules/Controller/Me/Personal/Report/PersonalReportViewController.swift`

**核心功能**:
- 支持个人信息举报和作品举报两种类型切换
- 使用阴影样式的容器（参考提供的阴影代码）
- 动态加载和显示API数据
- 单选/多选逻辑处理
- 提交按钮状态管理

### 4. UI设计特点

#### 阴影容器样式
```swift
// 参考提供的阴影样式代码实现
containerView.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
containerView.layer.shadowOpacity = 1
containerView.layer.shadowRadius = 4
```

#### 举报类型选择
- 个人信息举报：头像、昵称等信息违规
- 作品举报：针对该账号盗播或发布的不正当内容举报
- 使用单选按钮切换，支持橙色选中状态

#### 个人信息举报界面
- **举报内容**（可多选）：根据API动态显示选项
  - 头像违规
  - 昵称违规
  - 签名违规
  - 背景图片违规
- **举报理由**（仅单选）：根据API动态显示选项
  - 色情低俗
  - 不实信息
  - 违规
- **提交按钮**：选择完成后可提交

#### 作品举报界面（预留）
- 显示作品列表多选
- 按钮文字变为"下一步"
- 后续连接到理由选择页面

### 5. 按钮状态管理
- **不可交互状态**: 背景色 #E5E5E5，圆角 6pt
- **可交互状态**: 背景色 #FF6B35（橙色）
- **按钮尺寸**: 高度 43pt，左右各 16pt 边距

### 6. 集成到个人主页
**修改文件**: `Shuxiaoqi/Modules/Controller/Me/Personal/Controller/PersonalHomepageViewController.swift`

将举报功能连接到PersonalMoreActionSheet的举报按钮，点击后跳转到举报页面。

## API数据结构

### 举报内容API响应
```json
{
  "status": 1,
  "errMsg": "",
  "data": {
    "dictDetails": [
      {
        "id": 32,
        "label": "头像违规",
        "value": "report_user_info_content_1",
        "dictSort": 1
      }
    ]
  }
}
```

### 举报理由API响应
```json
{
  "status": 1,
  "errMsg": "",
  "data": {
    "dictDetails": [
      {
        "id": 36,
        "label": "色情低俗",
        "value": "report_user_info_reason_1",
        "dictSort": 1
      }
    ]
  }
}
```

## 使用的图片资源
- 单选按钮未选中: `app_radio_Default`
- 单选按钮已选中: `app_radio_select`
- 返回按钮: `nav_back`

## 测试文件
**文件**: `Shuxiaoqi/Test/PersonalReportTest.swift`
- 提供举报功能的测试界面
- 包含API接口测试功能

## 待完成功能

1. **作品举报界面**
   - 实现作品列表多选功能
   - 连接到下一步理由选择页面

2. **举报提交API**
   - 实现实际的举报提交接口调用
   - 处理提交成功/失败的反馈

3. **错误处理优化**
   - 网络错误提示
   - 数据加载失败处理

## 代码质量
- 遵循项目现有的代码风格和架构
- 使用AutoLayout进行界面布局
- 支持动态数据加载和UI更新
- 内存管理使用weak self避免循环引用
- 良好的错误处理和日志输出
