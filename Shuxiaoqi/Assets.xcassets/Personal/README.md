# 个人主页相关图片资源

## 拉黑功能图片资源命名规范

### 预命名的拉黑图片资源
- **资源名称**: `personal_action_block`
- **用途**: 个人主页更多弹窗中的拉黑操作图标
- **规格**: 22x22pt
- **格式**: PNG
- **需要的文件**:
  - `<EMAIL>` (44x44px)
  - `<EMAIL>` (66x66px)

### 建议的图标设计
- 颜色: 建议使用深色图标 (#333333) 或红色 (#FF1515) 以表示危险操作
- 风格: 与现有的 `comment_action_report` 和 `video_more_action_share` 保持一致
- 内容: 可以是禁止符号、屏蔽图标或类似的表示拉黑/屏蔽的图标

### 文件结构
```
Shuxiaoqi/Assets.xcassets/Personal/
├── personal_action_block.imageset/
│   ├── Contents.json
│   ├── <EMAIL>
│   └── <EMAIL>
└── README.md
```

### Contents.json 模板
```json
{
  "images" : [
    {
      "idiom" : "universal",
      "scale" : "1x"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "universal",
      "scale" : "2x"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "universal",
      "scale" : "3x"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}
```

## 使用说明
1. 将拉黑图片资源按照上述命名规范添加到项目中
2. 确保图片资源添加到 `Shuxiaoqi/Assets.xcassets/Personal/personal_action_block.imageset/` 目录
3. 图片资源添加完成后，`PersonalMoreActionSheet` 中的拉黑图标将自动显示
