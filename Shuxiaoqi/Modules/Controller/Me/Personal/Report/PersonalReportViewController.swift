import UIKit

/// 个人主页举报类型
enum PersonalReportType {
    case userInfo   // 个人信息举报
    case works      // 作品举报
}

/// 个人主页举报控制器
class PersonalReportViewController: BaseViewController {
    
    // MARK: - 属性
    private var userId: String
    private var currentReportType: PersonalReportType = .userInfo
    
    // UI组件
    private var scrollView: UIScrollView!
    private var contentView: UIView!
    private var reportTypeContainerView: UIView!
    private var userInfoReportButton: UIButton!
    private var worksReportButton: UIButton!
    private var userInfoRadioButton: UIButton!
    private var worksRadioButton: UIButton!
    
    private var contentContainerView: UIView!
    private var submitButton: UIButton!
    
    // 个人信息举报相关
    private var reportContentContainerView: UIView!
    private var reportReasonContainerView: UIView!
    private var reportContentData: [ReportUserInfoContentDetail] = []
    private var reportReasonData: [ReportUserInfoReasonDetail] = []
    private var selectedContentIndex: Int?
    private var selectedReasonIndex: Int?
    
    // MARK: - 初始化
    init(userId: String) {
        self.userId = userId
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupNavigationBar()
        loadReportData()
    }
    
    // MARK: - UI设置
    private func setupNavigationBar() {
        title = "举报类型"
        
        // 返回按钮
        let backButton = UIButton(type: .custom)
        backButton.setImage(UIImage(named: "nav_back"), for: .normal)
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)
        navigationItem.leftBarButtonItem = UIBarButtonItem(customView: backButton)
    }
    
    private func setupUI() {
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 滚动视图
        scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leftAnchor.constraint(equalTo: view.leftAnchor),
            scrollView.rightAnchor.constraint(equalTo: view.rightAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        // 内容视图
        contentView = UIView()
        scrollView.addSubview(contentView)
        contentView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leftAnchor.constraint(equalTo: scrollView.leftAnchor),
            contentView.rightAnchor.constraint(equalTo: scrollView.rightAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor)
        ])
        
        setupReportTypeSelection()
        setupContentContainer()
        setupSubmitButton()
        
        // 设置内容视图高度约束
        let heightConstraint = contentView.heightAnchor.constraint(greaterThanOrEqualTo: view.heightAnchor, constant: -view.safeAreaInsets.top - view.safeAreaInsets.bottom)
        heightConstraint.priority = UILayoutPriority(999)
        heightConstraint.isActive = true
    }
    
    private func setupReportTypeSelection() {
        // 举报类型容器（带阴影）
        reportTypeContainerView = createShadowContainer()
        contentView.addSubview(reportTypeContainerView)
        reportTypeContainerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            reportTypeContainerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 16),
            reportTypeContainerView.leftAnchor.constraint(equalTo: contentView.leftAnchor, constant: 16),
            reportTypeContainerView.rightAnchor.constraint(equalTo: contentView.rightAnchor, constant: -16),
            reportTypeContainerView.heightAnchor.constraint(equalToConstant: 120)
        ])
        
        // 个人信息举报选项
        setupReportOption(
            in: reportTypeContainerView,
            title: "个人信息举报",
            subtitle: "头像、昵称等信息违规",
            isTop: true,
            button: &userInfoReportButton,
            radioButton: &userInfoRadioButton,
            action: #selector(userInfoReportTapped)
        )
        
        // 分割线
        let separatorLine = UIView()
        separatorLine.backgroundColor = UIColor(hex: "#E7E7E7", alpha: 0.6)
        reportTypeContainerView.addSubview(separatorLine)
        separatorLine.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            separatorLine.centerYAnchor.constraint(equalTo: reportTypeContainerView.centerYAnchor),
            separatorLine.leftAnchor.constraint(equalTo: reportTypeContainerView.leftAnchor, constant: 16),
            separatorLine.rightAnchor.constraint(equalTo: reportTypeContainerView.rightAnchor, constant: -16),
            separatorLine.heightAnchor.constraint(equalToConstant: 1)
        ])
        
        // 作品举报选项
        setupReportOption(
            in: reportTypeContainerView,
            title: "作品举报",
            subtitle: "针对该账号盗播或发布的不正当内容举报",
            isTop: false,
            button: &worksReportButton,
            radioButton: &worksRadioButton,
            action: #selector(worksReportTapped)
        )
        
        // 默认选中个人信息举报
        updateReportTypeSelection()
    }
    
    private func setupContentContainer() {
        contentContainerView = UIView()
        contentView.addSubview(contentContainerView)
        contentContainerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            contentContainerView.topAnchor.constraint(equalTo: reportTypeContainerView.bottomAnchor, constant: 16),
            contentContainerView.leftAnchor.constraint(equalTo: contentView.leftAnchor),
            contentContainerView.rightAnchor.constraint(equalTo: contentView.rightAnchor),
            contentContainerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -100)
        ])
        
        setupUserInfoReportContent()
    }
    
    private func setupSubmitButton() {
        submitButton = UIButton(type: .custom)
        submitButton.setTitle("提交", for: .normal)
        submitButton.setTitleColor(.white, for: .normal)
        submitButton.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        submitButton.backgroundColor = UIColor(hex: "#E5E5E5") // 默认不可交互状态
        submitButton.layer.cornerRadius = 6
        submitButton.isEnabled = false
        submitButton.addTarget(self, action: #selector(submitButtonTapped), for: .touchUpInside)
        
        view.addSubview(submitButton)
        submitButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            submitButton.leftAnchor.constraint(equalTo: view.leftAnchor, constant: 16),
            submitButton.rightAnchor.constraint(equalTo: view.rightAnchor, constant: -16),
            submitButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -16),
            submitButton.heightAnchor.constraint(equalToConstant: 43)
        ])
    }

    // MARK: - 辅助方法
    private func createShadowContainer() -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white
        containerView.layer.cornerRadius = 8
        containerView.layer.shadowColor = UIColor(red: 0, green: 0, blue: 0, alpha: 0.1).cgColor
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowOpacity = 1
        containerView.layer.shadowRadius = 4
        return containerView
    }

    private func setupReportOption(in container: UIView, title: String, subtitle: String, isTop: Bool, button: inout UIButton!, radioButton: inout UIButton!, action: Selector) {
        button = UIButton(type: .custom)
        button.addTarget(self, action: action, for: .touchUpInside)
        container.addSubview(button)

        // 单选按钮
        radioButton = UIButton(type: .custom)
        radioButton.setImage(UIImage(named: "app_radio_Default"), for: .normal)
        radioButton.setImage(UIImage(named: "app_radio_select"), for: .selected)
        radioButton.isUserInteractionEnabled = false
        button.addSubview(radioButton)

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        button.addSubview(titleLabel)

        // 副标题
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = .systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor(hex: "#999999")
        subtitleLabel.numberOfLines = 0
        button.addSubview(subtitleLabel)

        // 布局
        button.translatesAutoresizingMaskIntoConstraints = false
        radioButton.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        let topConstant: CGFloat = isTop ? 16 : 8
        let bottomConstant: CGFloat = isTop ? -8 : -16

        NSLayoutConstraint.activate([
            button.topAnchor.constraint(equalTo: isTop ? container.topAnchor : container.centerYAnchor, constant: topConstant),
            button.leftAnchor.constraint(equalTo: container.leftAnchor),
            button.rightAnchor.constraint(equalTo: container.rightAnchor),
            button.bottomAnchor.constraint(equalTo: isTop ? container.centerYAnchor : container.bottomAnchor, constant: bottomConstant),

            radioButton.rightAnchor.constraint(equalTo: button.rightAnchor, constant: -16),
            radioButton.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            radioButton.widthAnchor.constraint(equalToConstant: 20),
            radioButton.heightAnchor.constraint(equalToConstant: 20),

            titleLabel.leftAnchor.constraint(equalTo: button.leftAnchor, constant: 16),
            titleLabel.topAnchor.constraint(equalTo: button.topAnchor, constant: 8),
            titleLabel.rightAnchor.constraint(equalTo: radioButton.leftAnchor, constant: -8),

            subtitleLabel.leftAnchor.constraint(equalTo: titleLabel.leftAnchor),
            subtitleLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 4),
            subtitleLabel.rightAnchor.constraint(equalTo: titleLabel.rightAnchor),
            subtitleLabel.bottomAnchor.constraint(lessThanOrEqualTo: button.bottomAnchor, constant: -8)
        ])
    }

    private func setupUserInfoReportContent() {
        // 清空容器
        contentContainerView.subviews.forEach { $0.removeFromSuperview() }

        // 举报内容容器
        reportContentContainerView = createShadowContainer()
        contentContainerView.addSubview(reportContentContainerView)
        reportContentContainerView.translatesAutoresizingMaskIntoConstraints = false

        // 举报理由容器
        reportReasonContainerView = createShadowContainer()
        contentContainerView.addSubview(reportReasonContainerView)
        reportReasonContainerView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            reportContentContainerView.topAnchor.constraint(equalTo: contentContainerView.topAnchor),
            reportContentContainerView.leftAnchor.constraint(equalTo: contentContainerView.leftAnchor, constant: 16),
            reportContentContainerView.rightAnchor.constraint(equalTo: contentContainerView.rightAnchor, constant: -16),

            reportReasonContainerView.topAnchor.constraint(equalTo: reportContentContainerView.bottomAnchor, constant: 16),
            reportReasonContainerView.leftAnchor.constraint(equalTo: contentContainerView.leftAnchor, constant: 16),
            reportReasonContainerView.rightAnchor.constraint(equalTo: contentContainerView.rightAnchor, constant: -16),
            reportReasonContainerView.bottomAnchor.constraint(lessThanOrEqualTo: contentContainerView.bottomAnchor)
        ])

        setupReportContentSection()
        setupReportReasonSection()
    }

    private func setupReportContentSection() {
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "举报内容"
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        reportContentContainerView.addSubview(titleLabel)

        let subtitleLabel = UILabel()
        subtitleLabel.text = "(可多选)"
        subtitleLabel.font = .systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor(hex: "#999999")
        reportContentContainerView.addSubview(subtitleLabel)

        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: reportContentContainerView.topAnchor, constant: 16),
            titleLabel.leftAnchor.constraint(equalTo: reportContentContainerView.leftAnchor, constant: 16),

            subtitleLabel.leftAnchor.constraint(equalTo: titleLabel.rightAnchor, constant: 4),
            subtitleLabel.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor)
        ])
    }

    private func setupReportReasonSection() {
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = "举报理由"
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor(hex: "#333333")
        reportReasonContainerView.addSubview(titleLabel)

        let subtitleLabel = UILabel()
        subtitleLabel.text = "(仅单选)"
        subtitleLabel.font = .systemFont(ofSize: 12)
        subtitleLabel.textColor = UIColor(hex: "#999999")
        reportReasonContainerView.addSubview(subtitleLabel)

        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        subtitleLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: reportReasonContainerView.topAnchor, constant: 16),
            titleLabel.leftAnchor.constraint(equalTo: reportReasonContainerView.leftAnchor, constant: 16),

            subtitleLabel.leftAnchor.constraint(equalTo: titleLabel.rightAnchor, constant: 4),
            subtitleLabel.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor)
        ])
    }

    // MARK: - 事件处理
    @objc private func backButtonTapped() {
        navigationController?.popViewController(animated: true)
    }

    @objc private func userInfoReportTapped() {
        currentReportType = .userInfo
        updateReportTypeSelection()
        setupUserInfoReportContent()
        updateSubmitButtonState()
    }

    @objc private func worksReportTapped() {
        currentReportType = .works
        updateReportTypeSelection()
        setupWorksReportContent()
        updateSubmitButtonState()
    }

    @objc private func submitButtonTapped() {
        switch currentReportType {
        case .userInfo:
            submitUserInfoReport()
        case .works:
            // TODO: 实现作品举报提交
            print("作品举报提交")
        }
    }

    private func updateReportTypeSelection() {
        userInfoRadioButton.isSelected = (currentReportType == .userInfo)
        worksRadioButton.isSelected = (currentReportType == .works)
    }

    private func setupWorksReportContent() {
        // 清空容器
        contentContainerView.subviews.forEach { $0.removeFromSuperview() }

        // TODO: 实现作品列表多选界面
        let placeholderLabel = UILabel()
        placeholderLabel.text = "作品举报功能待实现"
        placeholderLabel.textAlignment = .center
        placeholderLabel.textColor = UIColor(hex: "#999999")
        contentContainerView.addSubview(placeholderLabel)
        placeholderLabel.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            placeholderLabel.centerXAnchor.constraint(equalTo: contentContainerView.centerXAnchor),
            placeholderLabel.centerYAnchor.constraint(equalTo: contentContainerView.centerYAnchor)
        ])

        // 更新提交按钮文字
        submitButton.setTitle("下一步", for: .normal)
    }

    private func updateSubmitButtonState() {
        let isEnabled: Bool
        let backgroundColor: UIColor

        switch currentReportType {
        case .userInfo:
            isEnabled = selectedContentIndex != nil && selectedReasonIndex != nil
            backgroundColor = isEnabled ? UIColor(hex: "#FF6B35") : UIColor(hex: "#E5E5E5")
            submitButton.setTitle("提交", for: .normal)
        case .works:
            isEnabled = false // 作品举报暂未实现
            backgroundColor = UIColor(hex: "#E5E5E5")
            submitButton.setTitle("下一步", for: .normal)
        }

        submitButton.isEnabled = isEnabled
        submitButton.backgroundColor = backgroundColor
    }

    // MARK: - 数据加载
    private func loadReportData() {
        loadReportContentData()
        loadReportReasonData()
    }

    private func loadReportContentData() {
        APIManager.shared.getReportUserInfoContentDict { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if let data = response.data {
                        self?.reportContentData = data.dictDetails.sorted { $0.dictSort < $1.dictSort }
                        self?.updateReportContentUI()
                    }
                case .failure(let error):
                    print("获取举报内容数据失败: \(error)")
                }
            }
        }
    }

    private func loadReportReasonData() {
        APIManager.shared.getReportUserInfoReasonDict { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if let data = response.data {
                        self?.reportReasonData = data.dictDetails.sorted { $0.dictSort < $1.dictSort }
                        self?.updateReportReasonUI()
                    }
                case .failure(let error):
                    print("获取举报理由数据失败: \(error)")
                }
            }
        }
    }

    private func updateReportContentUI() {
        guard currentReportType == .userInfo else { return }

        // 移除现有的选项按钮
        reportContentContainerView.subviews.filter { $0.tag >= 1000 }.forEach { $0.removeFromSuperview() }

        var lastView: UIView? = reportContentContainerView.subviews.first { $0 is UILabel }

        for (index, content) in reportContentData.enumerated() {
            let optionButton = createOptionButton(title: content.label, isSelected: false, tag: 1000 + index)
            optionButton.addTarget(self, action: #selector(reportContentOptionTapped(_:)), for: .touchUpInside)
            reportContentContainerView.addSubview(optionButton)

            optionButton.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                optionButton.topAnchor.constraint(equalTo: lastView?.bottomAnchor ?? reportContentContainerView.topAnchor, constant: lastView != nil ? 12 : 48),
                optionButton.leftAnchor.constraint(equalTo: reportContentContainerView.leftAnchor, constant: 16),
                optionButton.rightAnchor.constraint(equalTo: reportContentContainerView.rightAnchor, constant: -16),
                optionButton.heightAnchor.constraint(equalToConstant: 44)
            ])

            lastView = optionButton
        }

        // 更新容器高度
        if let lastView = lastView {
            lastView.bottomAnchor.constraint(equalTo: reportContentContainerView.bottomAnchor, constant: -16).isActive = true
        }
    }

    private func updateReportReasonUI() {
        guard currentReportType == .userInfo else { return }

        // 移除现有的选项按钮
        reportReasonContainerView.subviews.filter { $0.tag >= 2000 }.forEach { $0.removeFromSuperview() }

        var lastView: UIView? = reportReasonContainerView.subviews.first { $0 is UILabel }

        for (index, reason) in reportReasonData.enumerated() {
            let optionButton = createOptionButton(title: reason.label, isSelected: false, tag: 2000 + index)
            optionButton.addTarget(self, action: #selector(reportReasonOptionTapped(_:)), for: .touchUpInside)
            reportReasonContainerView.addSubview(optionButton)

            optionButton.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                optionButton.topAnchor.constraint(equalTo: lastView?.bottomAnchor ?? reportReasonContainerView.topAnchor, constant: lastView != nil ? 12 : 48),
                optionButton.leftAnchor.constraint(equalTo: reportReasonContainerView.leftAnchor, constant: 16),
                optionButton.rightAnchor.constraint(equalTo: reportReasonContainerView.rightAnchor, constant: -16),
                optionButton.heightAnchor.constraint(equalToConstant: 44)
            ])

            lastView = optionButton
        }

        // 更新容器高度
        if let lastView = lastView {
            lastView.bottomAnchor.constraint(equalTo: reportReasonContainerView.bottomAnchor, constant: -16).isActive = true
        }
    }

    private func createOptionButton(title: String, isSelected: Bool, tag: Int) -> UIButton {
        let button = UIButton(type: .custom)
        button.tag = tag

        // 单选/多选按钮
        let radioButton = UIButton(type: .custom)
        radioButton.setImage(UIImage(named: "app_radio_Default"), for: .normal)
        radioButton.setImage(UIImage(named: "app_radio_select"), for: .selected)
        radioButton.isSelected = isSelected
        radioButton.isUserInteractionEnabled = false
        button.addSubview(radioButton)

        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 16)
        titleLabel.textColor = UIColor(hex: "#333333")
        button.addSubview(titleLabel)

        // 布局
        radioButton.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            radioButton.leftAnchor.constraint(equalTo: button.leftAnchor),
            radioButton.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            radioButton.widthAnchor.constraint(equalToConstant: 20),
            radioButton.heightAnchor.constraint(equalToConstant: 20),

            titleLabel.leftAnchor.constraint(equalTo: radioButton.rightAnchor, constant: 12),
            titleLabel.centerYAnchor.constraint(equalTo: button.centerYAnchor),
            titleLabel.rightAnchor.constraint(lessThanOrEqualTo: button.rightAnchor)
        ])

        return button
    }

    // MARK: - 选项点击事件
    @objc private func reportContentOptionTapped(_ sender: UIButton) {
        let index = sender.tag - 1000

        // 多选逻辑
        if let currentIndex = selectedContentIndex, currentIndex == index {
            // 取消选择
            selectedContentIndex = nil
        } else {
            // 选择新项
            selectedContentIndex = index
        }

        // 更新UI
        updateContentOptionsUI()
        updateSubmitButtonState()
    }

    @objc private func reportReasonOptionTapped(_ sender: UIButton) {
        let index = sender.tag - 2000

        // 单选逻辑
        selectedReasonIndex = index

        // 更新UI
        updateReasonOptionsUI()
        updateSubmitButtonState()
    }

    private func updateContentOptionsUI() {
        for (index, _) in reportContentData.enumerated() {
            if let button = reportContentContainerView.viewWithTag(1000 + index) as? UIButton,
               let radioButton = button.subviews.first(where: { $0 is UIButton }) as? UIButton {
                radioButton.isSelected = (selectedContentIndex == index)
            }
        }
    }

    private func updateReasonOptionsUI() {
        for (index, _) in reportReasonData.enumerated() {
            if let button = reportReasonContainerView.viewWithTag(2000 + index) as? UIButton,
               let radioButton = button.subviews.first(where: { $0 is UIButton }) as? UIButton {
                radioButton.isSelected = (selectedReasonIndex == index)
            }
        }
    }

    // MARK: - 提交举报
    private func submitUserInfoReport() {
        guard let contentIndex = selectedContentIndex,
              let reasonIndex = selectedReasonIndex,
              contentIndex < reportContentData.count,
              reasonIndex < reportReasonData.count else {
            return
        }

        let selectedContent = reportContentData[contentIndex]
        let selectedReason = reportReasonData[reasonIndex]

        print("提交个人信息举报:")
        print("用户ID: \(userId)")
        print("举报内容: \(selectedContent.label) (\(selectedContent.value))")
        print("举报理由: \(selectedReason.label) (\(selectedReason.value))")

        // TODO: 调用举报提交API
        showSuccessAlert()
    }

    private func showSuccessAlert() {
        let alert = UIAlertController(title: "举报成功", message: "感谢您的举报，我们会尽快处理", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            self?.navigationController?.popViewController(animated: true)
        })
        present(alert, animated: true)
    }
}
