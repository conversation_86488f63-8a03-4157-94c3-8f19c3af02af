import SmartCodable

// MARK: - 个人信息举报理由响应模型
struct ReportUserInfoReasonResponse: SmartCodable {
    let status: Int
    let errMsg: String
    let data: ReportUserInfoReasonData?
}

// MARK: - 个人信息举报理由数据
struct ReportUserInfoReasonData: SmartCodable {
    let createBy: String
    let updateBy: String
    let createTime: String
    let updateTime: String
    let id: Int
    let dictDetails: [ReportUserInfoReasonDetail]
    let name: String
    let description: String
}

// MARK: - 个人信息举报理由详情
struct ReportUserInfoReasonDetail: SmartCodable {
    let createBy: String
    let updateBy: String
    let createTime: String
    let updateTime: String
    let id: Int
    let label: String
    let value: String
    let dictSort: Int
    
    /// 是否被选中（用于UI状态管理）
    var isSelected: Bool = false
    
    enum CodingKeys: String, CodingKey {
        case createBy, updateBy, createTime, updateTime, id, label, value, dictSort
    }
}

// MARK: - 便利方法扩展
extension ReportUserInfoReasonDetail {
    /// 获取显示标题
    var displayTitle: String {
        return label
    }
    
    /// 获取举报理由类型
    var reasonType: ReportReasonType {
        switch value {
        case "report_user_info_reason_1":
            return .pornographic
        case "report_user_info_reason_2":
            return .falseInformation
        case "report_user_info_reason_3":
            return .violation
        default:
            return .other
        }
    }
}

// MARK: - 举报理由类型枚举
enum ReportReasonType {
    case pornographic      // 色情低俗
    case falseInformation  // 不实信息
    case violation         // 违规
    case harassment        // 人身攻击
    case spam             // 违禁、赌博诈骗
    case inducementFlow   // 违规引流
    case other            // 其他
    
    var description: String {
        switch self {
        case .pornographic:
            return "色情低俗"
        case .falseInformation:
            return "不实信息"
        case .violation:
            return "违规"
        case .harassment:
            return "人身攻击"
        case .spam:
            return "违禁、赌博诈骗"
        case .inducementFlow:
            return "违规引流"
        case .other:
            return "其他"
        }
    }
}
