import SmartCodable

// MARK: - 个人信息举报理由响应模型
struct ReportUserInfoReasonResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var data: ReportUserInfoReasonData?

    init() {}
}

// MARK: - 个人信息举报理由数据
struct ReportUserInfoReasonData: SmartCodable {
    var createBy: String = ""
    var updateBy: String = ""
    var createTime: String = ""
    var updateTime: String = ""
    var id: Int = 0
    var dictDetails: [ReportUserInfoReasonDetail] = []
    var name: String = ""
    var description: String = ""

    init() {}
}

// MARK: - 个人信息举报理由详情
struct ReportUserInfoReasonDetail: SmartCodable {
    var createBy: String = ""
    var updateBy: String = ""
    var createTime: String = ""
    var updateTime: String = ""
    var id: Int = 0
    var label: String = ""
    var value: String = ""
    var dictSort: Int = 0

    /// 是否被选中（用于UI状态管理）
    var isSelected: Bool = false

    init() {}
}

// MARK: - 便利方法扩展
extension ReportUserInfoReasonDetail {
    /// 获取显示标题
    var displayTitle: String {
        return label
    }
    
    /// 获取举报理由类型
    var reasonType: ReportReasonType {
        switch value {
        case "report_user_info_reason_1":
            return .pornographic
        case "report_user_info_reason_2":
            return .falseInformation
        case "report_user_info_reason_3":
            return .violation
        default:
            return .other
        }
    }
}

// MARK: - 举报理由类型枚举
enum ReportReasonType {
    case pornographic      // 色情低俗
    case falseInformation  // 不实信息
    case violation         // 违规
    case harassment        // 人身攻击
    case spam             // 违禁、赌博诈骗
    case inducementFlow   // 违规引流
    case other            // 其他
    
    var description: String {
        switch self {
        case .pornographic:
            return "色情低俗"
        case .falseInformation:
            return "不实信息"
        case .violation:
            return "违规"
        case .harassment:
            return "人身攻击"
        case .spam:
            return "违禁、赌博诈骗"
        case .inducementFlow:
            return "违规引流"
        case .other:
            return "其他"
        }
    }
}
