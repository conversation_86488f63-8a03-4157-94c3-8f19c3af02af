import SmartCodable

// MARK: - 个人信息举报内容响应模型
struct ReportUserInfoContentResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var data: ReportUserInfoContentData?

    init() {}
}

// MARK: - 个人信息举报内容数据
struct ReportUserInfoContentData: SmartCodable {
    var createBy: String = ""
    var updateBy: String = ""
    var createTime: String = ""
    var updateTime: String = ""
    var id: Int = 0
    var dictDetails: [ReportUserInfoContentDetail] = []
    var name: String = ""
    var description: String = ""

    init() {}
}

// MARK: - 个人信息举报内容详情
struct ReportUserInfoContentDetail: SmartCodable {
    var createBy: String = ""
    var updateBy: String = ""
    var createTime: String = ""
    var updateTime: String = ""
    var id: Int = 0
    var label: String = ""
    var value: String = ""
    var dictSort: Int = 0

    /// 是否被选中（用于UI状态管理）
    var isSelected: Bool = false

    init() {}
}

// MARK: - 便利方法扩展
extension ReportUserInfoContentDetail {
    /// 获取显示标题
    var displayTitle: String {
        return label
    }
    
    /// 获取举报内容类型
    var contentType: ReportContentType {
        switch value {
        case "report_user_info_content_1":
            return .avatar
        case "report_user_info_content_2":
            return .nickname
        case "report_user_info_content_3":
            return .backgroundImage
        case "report_user_info_content_4":
            return .signature
        default:
            return .other
        }
    }
}

// MARK: - 举报内容类型枚举
enum ReportContentType {
    case avatar         // 头像违规
    case nickname       // 昵称违规
    case signature      // 签名违规
    case backgroundImage // 背景图片违规
    case other          // 其他
    
    var description: String {
        switch self {
        case .avatar:
            return "头像违规"
        case .nickname:
            return "昵称违规"
        case .signature:
            return "签名违规"
        case .backgroundImage:
            return "背景图片违规"
        case .other:
            return "其他"
        }
    }
}
