///  VideoArrayResponse.swift
///  Shuxiaoqi
///
///  Created by AI on 2025/07/16.
///

import SmartCodable

/// 针对 data 为数组结构的通用响应
struct VideoArrayResponse: SmartCodable {
    var data: [VideoItem]?
    var errMsg: String?
    var msg: String?
    var status: Int?
    
    var isSuccess: Bool {
        return status == 200
    }
    
    /// 统一显示消息
    var displayMessage: String {
        if let m = msg, !m.isEmpty { return m }
        if let e = errMsg, !e.isEmpty { return e }
        return isSuccess ? "成功" : "未知错误"
    }
}
