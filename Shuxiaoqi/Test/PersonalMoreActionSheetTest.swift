import UIKit

/// 测试个人主页更多弹窗的简单测试控制器
class PersonalMoreActionSheetTestViewController: UIViewController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        title = "个人主页弹窗测试"
        
        // 添加测试按钮
        let testButton = UIButton(type: .system)
        testButton.setTitle("显示个人主页更多弹窗", for: .normal)
        testButton.titleLabel?.font = .systemFont(ofSize: 16)
        testButton.backgroundColor = .systemBlue
        testButton.setTitleColor(.white, for: .normal)
        testButton.layer.cornerRadius = 8
        testButton.addTarget(self, action: #selector(showActionSheet), for: .touchUpInside)
        
        view.addSubview(testButton)
        testButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            testButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            testButton.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            testButton.widthAnchor.constraint(equalToConstant: 200),
            testButton.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    @objc private func showActionSheet() {
        let actionSheet = PersonalMoreActionSheet { [weak self] actionType in
            self?.handleAction(actionType)
        }
        actionSheet.show(in: view)
    }
    
    private func handleAction(_ actionType: PersonalMoreActionType) {
        let message: String
        switch actionType {
        case .share:
            message = "分享功能被触发"
        case .report:
            message = "举报功能被触发"
        case .block:
            message = "拉黑功能被触发"
        }
        
        let alert = UIAlertController(title: "测试结果", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", for: .default))
        present(alert, animated: true)
    }
}
