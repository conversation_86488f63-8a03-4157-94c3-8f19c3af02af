import UIKit

/// 测试个人主页举报功能的简单测试控制器
class PersonalReportTestViewController: UIViewController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        title = "个人主页举报测试"
        
        // 添加测试按钮
        let testButton = UIButton(type: .system)
        testButton.setTitle("打开个人主页举报", for: .normal)
        testButton.titleLabel?.font = .systemFont(ofSize: 16)
        testButton.backgroundColor = .systemBlue
        testButton.setTitleColor(.white, for: .normal)
        testButton.layer.cornerRadius = 8
        testButton.addTarget(self, action: #selector(openPersonalReport), for: .touchUpInside)
        
        view.addSubview(testButton)
        testButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            testButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            testButton.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            testButton.widthAnchor.constraint(equalToConstant: 200),
            testButton.heightAnchor.constraint(equalToConstant: 44)
        ])
        
        // 添加API测试按钮
        let apiTestButton = UIButton(type: .system)
        apiTestButton.setTitle("测试举报API", for: .normal)
        apiTestButton.titleLabel?.font = .systemFont(ofSize: 16)
        apiTestButton.backgroundColor = .systemGreen
        apiTestButton.setTitleColor(.white, for: .normal)
        apiTestButton.layer.cornerRadius = 8
        apiTestButton.addTarget(self, action: #selector(testReportAPI), for: .touchUpInside)
        
        view.addSubview(apiTestButton)
        apiTestButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            apiTestButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            apiTestButton.topAnchor.constraint(equalTo: testButton.bottomAnchor, constant: 20),
            apiTestButton.widthAnchor.constraint(equalToConstant: 200),
            apiTestButton.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    @objc private func openPersonalReport() {
        let reportVC = PersonalReportViewController(userId: "test_user_123")
        navigationController?.pushViewController(reportVC, animated: true)
    }
    
    @objc private func testReportAPI() {
        print("开始测试举报API...")
        
        // 测试获取举报内容
        APIManager.shared.getReportUserInfoContentDict { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    print("✅ 举报内容API测试成功:")
                    print("状态: \(response.status)")
                    print("数据: \(response.data?.dictDetails.count ?? 0) 个选项")
                    response.data?.dictDetails.forEach { detail in
                        print("- \(detail.label): \(detail.value)")
                    }
                case .failure(let error):
                    print("❌ 举报内容API测试失败: \(error)")
                }
            }
        }
        
        // 测试获取举报理由
        APIManager.shared.getReportUserInfoReasonDict { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    print("✅ 举报理由API测试成功:")
                    print("状态: \(response.status)")
                    print("数据: \(response.data?.dictDetails.count ?? 0) 个选项")
                    response.data?.dictDetails.forEach { detail in
                        print("- \(detail.label): \(detail.value)")
                    }
                case .failure(let error):
                    print("❌ 举报理由API测试失败: \(error)")
                }
            }
        }
    }
}
